[package]
name = "orebs"
version = "1.0.16"
edition = "2021"

[profile.release]
opt-level = 3        # 最高级别的优化
lto = true           # 启用链接时间优化
codegen-units = 1    # 单一代码生成单元以提高优化效果
debug = false        # 不包含调试信息
rpath = false        # 禁用运行时搜索路径
panic = 'abort'      # 使用 abort 而不是 unwind 的 panic 策略
incremental = false  # 禁用增量编译

[dependencies]
axum = "0.8.4"
tokio = { version = "1.45.1", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
log = "0.4.27"
flexi_logger = "0.30.2"
chrono = "0.4.41"
rsntp = "4.0.0"
toml = "0.8.22"
tower = "0.5.2"
once_cell = "1.21.3"
retry = "2.1.0"
reqwest = { version = "0.12.19", default-features = false, features = ["json", "rustls-tls"] }
url = "2.5.4"
config = "0.15.11"
thiserror = "2.0.12"
validator = { version = "0.20.0", features = ["derive"] }
sysinfo = "0.35.2"
tower-http = { version = "0.6.6", features = ["trace"] }
tabled = { version = "0.19.0", features = ["std", "derive", "ansi"] }
nix = { version = "0.30.1", features = ["user"] }
# WebSocket 相关依赖
axum-tungstenite = "0.1.0"
tokio-tungstenite = "0.24.0"
futures-util = "0.3.31"
uuid = { version = "1.11.0", features = ["v4"] }