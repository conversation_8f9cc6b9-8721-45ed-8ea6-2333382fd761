use crate::{
    basic::config::types::Settings,
    handlers::{done_oren_logic, init_oren_logic, start_oren_logic, verify_token_logic},
};
use axum::{
    extract::{
        ws::{Message, WebSocket},
        State, WebSocketUpgrade,
    },
    response::Response,
};
use axum_tungstenite::WebSocketUpgrade as AxumWebSocketUpgrade;
use futures_util::{sink::SinkExt, stream::StreamExt};
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::{
    collections::VecDeque,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{mpsc, Mutex};
use uuid::Uuid;

/// WebSocket 消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessageType {
    /// 验证 Token
    #[serde(rename = "verify_token")]
    VerifyToken,
    /// 初始化 OpenResty Edge Node
    #[serde(rename = "init_oren")]
    InitOren,
    /// 添加 OpenResty Edge Node
    #[serde(rename = "add_oren")]
    AddOren,
    /// 启动 OpenResty Edge Node
    #[serde(rename = "start_oren")]
    StartOren,
    /// 完成 OpenResty Edge Node
    #[serde(rename = "done_oren")]
    DoneOren,
}

/// 客户端请求消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientMessage {
    /// 消息 ID，用于关联请求和响应
    pub id: String,
    /// 消息类型
    #[serde(flatten)]
    pub message_type: WsMessageType,
    /// 认证 Token
    pub token: Option<String>,
}

/// 服务端响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerMessage {
    /// 对应的请求消息 ID
    pub id: String,
    /// 响应代码: 0 表示成功, 非 0 表示失败
    pub code: i32,
    /// 响应消息
    pub msg: String,
    /// 消息类型（用于客户端识别响应类型）
    #[serde(flatten)]
    pub message_type: WsMessageType,
}

/// 系统通知消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMessage {
    /// 通知类型
    #[serde(rename = "type")]
    pub msg_type: String,
    /// 通知内容
    pub msg: String,
}

/// 客户端连接信息
#[derive(Debug)]
pub struct ClientConnection {
    /// 客户端 ID
    pub id: Uuid,
    /// 连接建立时间
    pub connected_at: Instant,
    /// 消息发送通道
    pub sender: mpsc::UnboundedSender<Message>,
}

/// 等待队列中的客户端
#[derive(Debug)]
pub struct QueuedClient {
    /// 客户端 ID
    pub id: Uuid,
    /// WebSocket 升级响应
    pub upgrade: WebSocketUpgrade,
    /// 设置信息
    pub settings: Arc<Settings>,
}

/// WebSocket 连接管理器
pub struct ConnectionManager {
    /// 当前活跃的客户端连接
    pub active_client: Arc<Mutex<Option<ClientConnection>>>,
    /// 等待队列
    pub waiting_queue: Arc<Mutex<VecDeque<QueuedClient>>>,
    /// 队列通知通道
    pub queue_notifier: mpsc::UnboundedSender<()>,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> (Self, mpsc::UnboundedReceiver<()>) {
        let (queue_notifier, queue_receiver) = mpsc::unbounded_channel();
        
        (
            Self {
                active_client: Arc::new(Mutex::new(None)),
                waiting_queue: Arc::new(Mutex::new(VecDeque::new())),
                queue_notifier,
            },
            queue_receiver,
        )
    }

    /// 尝试连接客户端
    pub async fn try_connect(
        &self,
        upgrade: WebSocketUpgrade,
        settings: Arc<Settings>,
    ) -> Response {
        let client_id = Uuid::new_v4();
        info!("客户端 {} 请求连接", client_id);

        // 检查是否有活跃连接
        let mut active_client = self.active_client.lock().await;
        if active_client.is_some() {
            // 有活跃连接，将新客户端加入等待队列
            drop(active_client); // 释放锁
            
            let mut queue = self.waiting_queue.lock().await;
            queue.push_back(QueuedClient {
                id: client_id,
                upgrade,
                settings,
            });
            let queue_position = queue.len();
            drop(queue);

            info!("客户端 {} 已加入等待队列，当前排队位置: {}", client_id, queue_position);
            
            // 返回等待响应
            upgrade.on_upgrade(move |socket| async move {
                Self::handle_waiting_client(socket, client_id, queue_position).await;
            })
        } else {
            // 没有活跃连接，直接建立连接
            info!("客户端 {} 直接建立连接", client_id);
            upgrade.on_upgrade(move |socket| async move {
                Self::handle_active_client(socket, client_id, settings).await;
            })
        }
    }

    /// 处理等待中的客户端
    async fn handle_waiting_client(socket: WebSocket, client_id: Uuid, queue_position: usize) {
        info!("等待中的客户端 {} 连接已建立", client_id);
        
        let (mut sender, mut receiver) = socket.split();
        
        // 发送等待通知
        let wait_msg = SystemMessage {
            msg_type: "waiting".to_string(),
            msg: format!("您当前排队位置: {}，请耐心等待", queue_position),
        };
        
        if let Ok(msg_json) = serde_json::to_string(&wait_msg) {
            if let Err(e) = sender.send(Message::Text(msg_json)).await {
                error!("向等待客户端 {} 发送消息失败: {}", client_id, e);
                return;
            }
        }

        // 保持连接直到轮到该客户端或连接断开
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Close(_)) => {
                    info!("等待客户端 {} 主动断开连接", client_id);
                    break;
                }
                Ok(Message::Ping(data)) => {
                    if let Err(e) = sender.send(Message::Pong(data)).await {
                        error!("向等待客户端 {} 发送 Pong 失败: {}", client_id, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("等待客户端 {} 连接错误: {}", client_id, e);
                    break;
                }
                _ => {
                    // 忽略其他消息类型
                }
            }
        }
        
        info!("等待客户端 {} 连接已关闭", client_id);
    }

    /// 处理活跃客户端
    async fn handle_active_client(socket: WebSocket, client_id: Uuid, settings: Arc<Settings>) {
        info!("活跃客户端 {} 连接已建立", client_id);
        
        let (mut sender, mut receiver) = socket.split();
        let (tx, mut rx) = mpsc::unbounded_channel();
        
        // 创建客户端连接信息
        let connection = ClientConnection {
            id: client_id,
            connected_at: Instant::now(),
            sender: tx,
        };

        // 发送连接成功通知
        let connect_msg = SystemMessage {
            msg_type: "connected".to_string(),
            msg: "连接成功，可以开始发送命令".to_string(),
        };
        
        if let Ok(msg_json) = serde_json::to_string(&connect_msg) {
            if let Err(e) = sender.send(Message::Text(msg_json)).await {
                error!("向活跃客户端 {} 发送连接成功消息失败: {}", client_id, e);
                return;
            }
        }

        // 启动消息发送任务
        let client_id_for_sender = client_id;
        tokio::spawn(async move {
            while let Some(message) = rx.recv().await {
                if let Err(e) = sender.send(message).await {
                    error!("向客户端 {} 发送消息失败: {}", client_id_for_sender, e);
                    break;
                }
            }
        });

        // 处理接收到的消息
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    Self::handle_client_message(text, &connection, &settings).await;
                }
                Ok(Message::Close(_)) => {
                    info!("活跃客户端 {} 主动断开连接", client_id);
                    break;
                }
                Ok(Message::Ping(data)) => {
                    if let Err(e) = connection.sender.send(Message::Pong(data)) {
                        error!("向活跃客户端 {} 发送 Pong 失败: {}", client_id, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("活跃客户端 {} 连接错误: {}", client_id, e);
                    break;
                }
                _ => {
                    // 忽略其他消息类型
                }
            }
        }
        
        info!("活跃客户端 {} 连接已关闭", client_id);
    }

    /// 处理客户端消息
    async fn handle_client_message(
        text: String,
        connection: &ClientConnection,
        settings: &Arc<Settings>,
    ) {
        // 解析客户端消息
        let client_msg: ClientMessage = match serde_json::from_str(&text) {
            Ok(msg) => msg,
            Err(e) => {
                error!("解析客户端消息失败: {}", e);
                return;
            }
        };

        info!("收到客户端 {} 的消息: {:?}", connection.id, client_msg.message_type);

        // 验证 Token（除了 verify_token 操作）
        if !matches!(client_msg.message_type, WsMessageType::VerifyToken) {
            if let Err(e) = Self::validate_token(&client_msg.token, settings).await {
                let response = ServerMessage {
                    id: client_msg.id,
                    code: 1,
                    msg: format!("认证失败: {}", e),
                    message_type: client_msg.message_type,
                };
                Self::send_response(connection, response).await;
                return;
            }
        }

        // 执行对应的操作
        let (code, msg) = match client_msg.message_type {
            WsMessageType::VerifyToken => {
                match Self::validate_token(&client_msg.token, settings).await {
                    Ok(_) => (0, "Token 验证成功".to_string()),
                    Err(e) => (1, format!("Token 验证失败: {}", e)),
                }
            }
            WsMessageType::InitOren => {
                match init_oren_logic().await {
                    Ok(_) => (0, "OpenResty Edge Node 初始化成功".to_string()),
                    Err(e) => (1, format!("OpenResty Edge Node 初始化失败: {}", e)),
                }
            }
            WsMessageType::AddOren => {
                // add_oren 是异步操作，立即返回成功
                tokio::spawn(Self::handle_add_oren_async(connection.sender.clone(), client_msg.id.clone(), settings.clone()));
                (0, "已启动添加 OpenResty Edge Node 流程".to_string())
            }
            WsMessageType::StartOren => {
                match start_oren_logic().await {
                    Ok(_) => (0, "OpenResty Edge Node 启动成功".to_string()),
                    Err(e) => (1, format!("OpenResty Edge Node 启动失败: {}", e)),
                }
            }
            WsMessageType::DoneOren => {
                match done_oren_logic().await {
                    Ok(_) => (0, "OpenResty Edge Node 配置完成".to_string()),
                    Err(e) => (1, format!("OpenResty Edge Node 配置失败: {}", e)),
                }
            }
        };

        let response = ServerMessage {
            id: client_msg.id,
            code,
            msg,
            message_type: client_msg.message_type,
        };

        Self::send_response(connection, response).await;
    }

    /// 验证 Token
    async fn validate_token(token: &Option<String>, settings: &Arc<Settings>) -> Result<(), String> {
        let token = token.as_ref().ok_or("未提供认证 Token")?;
        
        if !token.starts_with("Bearer ") {
            return Err("Token 格式错误".to_string());
        }

        let token_value = &token[7..];
        
        if !settings
            .api
            .auth_tokens
            .iter()
            .any(|valid_token| valid_token == token_value)
        {
            return Err("Token 不匹配".to_string());
        }

        let current_date = chrono::Utc::now().naive_utc();
        if current_date > settings.api.token_expiry {
            return Err(format!("Token 已于 {} 过期", settings.api.token_expiry));
        }

        Ok(())
    }

    /// 发送响应消息
    async fn send_response(connection: &ClientConnection, response: ServerMessage) {
        if let Ok(response_json) = serde_json::to_string(&response) {
            if let Err(e) = connection.sender.send(Message::Text(response_json)) {
                error!("向客户端 {} 发送响应失败: {}", connection.id, e);
            }
        }
    }

    /// 异步处理 add_oren 操作
    async fn handle_add_oren_async(
        sender: mpsc::UnboundedSender<Message>,
        request_id: String,
        settings: Arc<Settings>,
    ) {
        // 这里需要调用实际的 add_oren 逻辑
        // 由于原来的 add_oren 函数比较复杂，我们需要重构它
        info!("开始异步执行 add_oren 操作");
        
        // 发送进度通知
        let progress_msg = SystemMessage {
            msg_type: "progress".to_string(),
            msg: "正在执行添加 OpenResty Edge Node 操作，请耐心等待...".to_string(),
        };
        
        if let Ok(msg_json) = serde_json::to_string(&progress_msg) {
            let _ = sender.send(Message::Text(msg_json));
        }

        // 执行实际的添加逻辑（这里需要从 handlers.rs 中提取）
        // TODO: 实现 add_oren_logic 函数
        
        // 发送完成通知
        let complete_msg = ServerMessage {
            id: request_id,
            code: 0,
            msg: "OpenResty Edge Node 添加操作已完成".to_string(),
            message_type: WsMessageType::AddOren,
        };
        
        if let Ok(msg_json) = serde_json::to_string(&complete_msg) {
            let _ = sender.send(Message::Text(msg_json));
        }
    }
}

/// WebSocket 升级处理函数
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(settings): State<Arc<Settings>>,
) -> Response {
    // 这里需要访问全局的连接管理器
    // 暂时直接处理连接，后续需要集成到全局状态中
    ws.on_upgrade(move |socket| async move {
        let client_id = Uuid::new_v4();
        ConnectionManager::handle_active_client(socket, client_id, settings).await;
    })
}
