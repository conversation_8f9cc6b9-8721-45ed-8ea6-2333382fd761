use crate::{
    basic::config::types::Settings,
    handlers::{
        add_oren_logic, done_oren_logic, init_oren_logic, start_oren_logic, verify_token_logic,
    },
};

use axum::{
    extract::{
        ws::{Message, WebSocket},
        State, WebSocketUpgrade,
    },
    response::Response,
};
use futures_util::{sink::SinkExt, stream::StreamExt};
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::{
    collections::VecDeque,
    sync::Arc,
    time::{Duration, Instant},
};
use tokio::sync::{mpsc, Mutex};
use uuid::Uuid;

/// WebSocket 消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WsMessageType {
    /// 验证 Token
    #[serde(rename = "verify_token")]
    VerifyToken,
    /// 初始化 OpenResty Edge Node
    #[serde(rename = "init_oren")]
    InitOren,
    /// 添加 OpenResty Edge Node
    #[serde(rename = "add_oren")]
    AddOren,
    /// 启动 OpenResty Edge Node
    #[serde(rename = "start_oren")]
    StartOren,
    /// 完成 OpenResty Edge Node
    #[serde(rename = "done_oren")]
    DoneOren,
}

/// 客户端请求消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClientMessage {
    /// 消息 ID，用于关联请求和响应
    pub id: String,
    /// 消息类型
    #[serde(flatten)]
    pub message_type: WsMessageType,
    /// 认证 Token
    pub token: Option<String>,
}

/// 服务端响应消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerMessage {
    /// 对应的请求消息 ID
    pub id: String,
    /// 响应代码: 0 表示成功, 非 0 表示失败
    pub code: i32,
    /// 响应消息
    pub msg: String,
    /// 消息类型（用于客户端识别响应类型）
    #[serde(flatten)]
    pub message_type: WsMessageType,
}

/// 系统通知消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMessage {
    /// 通知类型
    #[serde(rename = "type")]
    pub msg_type: String,
    /// 通知内容
    pub msg: String,
    /// 额外数据（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<serde_json::Value>,
}

/// 客户端连接信息
#[derive(Debug)]
pub struct ClientConnection {
    /// 客户端 ID
    pub id: Uuid,
    /// 连接建立时间
    pub connected_at: Instant,
    /// 消息发送通道
    pub sender: mpsc::UnboundedSender<Message>,
}

/// 等待队列中的客户端
#[derive(Debug)]
pub struct QueuedClient {
    /// 客户端 ID
    pub id: Uuid,
    /// WebSocket 升级响应
    pub upgrade: WebSocketUpgrade,
    /// 设置信息
    pub settings: Arc<Settings>,
}

/// WebSocket 连接管理器
pub struct ConnectionManager {
    /// 当前活跃的客户端连接
    pub active_client: Arc<Mutex<Option<ClientConnection>>>,
    /// 等待队列
    pub waiting_queue: Arc<Mutex<VecDeque<QueuedClient>>>,
    /// 队列通知通道
    pub queue_notifier: mpsc::UnboundedSender<()>,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> (Self, mpsc::UnboundedReceiver<()>) {
        let (queue_notifier, queue_receiver) = mpsc::unbounded_channel();

        (
            Self {
                active_client: Arc::new(Mutex::new(None)),
                waiting_queue: Arc::new(Mutex::new(VecDeque::new())),
                queue_notifier,
            },
            queue_receiver,
        )
    }

    /// 尝试连接客户端
    pub async fn try_connect(
        &self,
        upgrade: WebSocketUpgrade,
        settings: Arc<Settings>,
    ) -> Response {
        let client_id = Uuid::new_v4();
        info!("客户端 {} 请求连接", client_id);

        // 检查是否有活跃连接
        let mut active_client = self.active_client.lock().await;
        if active_client.is_some() {
            // 有活跃连接，将新客户端加入等待队列
            drop(active_client); // 释放锁

            let mut queue = self.waiting_queue.lock().await;
            queue.push_back(QueuedClient {
                id: client_id,
                upgrade,
                settings,
            });
            let queue_position = queue.len();
            drop(queue);

            info!(
                "客户端 {} 已加入等待队列，当前排队位置: {}",
                client_id, queue_position
            );

            // 返回等待响应
            upgrade.on_upgrade(move |socket| async move {
                Self::handle_waiting_client(socket, client_id, queue_position).await;
            })
        } else {
            // 没有活跃连接，直接建立连接
            info!("客户端 {} 直接建立连接", client_id);
            upgrade.on_upgrade(move |socket| async move {
                Self::handle_active_client(socket, client_id, settings).await;
            })
        }
    }

    /// 处理等待中的客户端
    async fn handle_waiting_client(socket: WebSocket, client_id: Uuid, queue_position: usize) {
        info!("等待中的客户端 {} 连接已建立", client_id);

        let (mut sender, mut receiver) = socket.split();

        // 发送等待通知
        let wait_msg = SystemMessage {
            msg_type: "waiting".to_string(),
            msg: format!("您当前排队位置: {}，请耐心等待", queue_position),
            data: Some(serde_json::json!({
                "queue_position": queue_position
            })),
        };

        if let Ok(msg_json) = serde_json::to_string(&wait_msg) {
            if let Err(e) = sender.send(Message::Text(msg_json)).await {
                error!("向等待客户端 {} 发送消息失败: {}", client_id, e);
                return;
            }
        }

        // 保持连接直到轮到该客户端或连接断开
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Close(_)) => {
                    info!("等待客户端 {} 主动断开连接", client_id);
                    break;
                }
                Ok(Message::Ping(data)) => {
                    if let Err(e) = sender.send(Message::Pong(data)).await {
                        error!("向等待客户端 {} 发送 Pong 失败: {}", client_id, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("等待客户端 {} 连接错误: {}", client_id, e);
                    break;
                }
                _ => {
                    // 忽略其他消息类型
                }
            }
        }

        info!("等待客户端 {} 连接已关闭", client_id);
    }

    /// 处理活跃客户端
    async fn handle_active_client(socket: WebSocket, client_id: Uuid, settings: Arc<Settings>) {
        info!("活跃客户端 {} 连接已建立", client_id);

        let (mut sender, mut receiver) = socket.split();
        let (tx, mut rx) = mpsc::unbounded_channel();

        // 创建客户端连接信息
        let connection = ClientConnection {
            id: client_id,
            connected_at: Instant::now(),
            sender: tx,
        };

        // 发送连接成功通知
        let connect_msg = SystemMessage {
            msg_type: "connected".to_string(),
            msg: "连接成功，可以开始发送命令".to_string(),
            data: Some(serde_json::json!({
                "client_id": client_id.to_string(),
                "connected_at": connection.connected_at.elapsed().as_secs()
            })),
        };

        if let Ok(msg_json) = serde_json::to_string(&connect_msg) {
            if let Err(e) = sender.send(Message::Text(msg_json)).await {
                error!("向活跃客户端 {} 发送连接成功消息失败: {}", client_id, e);
                return;
            }
        }

        // 启动消息发送任务
        let client_id_for_sender = client_id;
        tokio::spawn(async move {
            while let Some(message) = rx.recv().await {
                if let Err(e) = sender.send(message).await {
                    error!("向客户端 {} 发送消息失败: {}", client_id_for_sender, e);
                    break;
                }
            }
        });

        // 处理接收到的消息
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    Self::handle_client_message(text, &connection, &settings).await;
                }
                Ok(Message::Close(_)) => {
                    info!("活跃客户端 {} 主动断开连接", client_id);
                    break;
                }
                Ok(Message::Ping(data)) => {
                    if let Err(e) = connection.sender.send(Message::Pong(data)) {
                        error!("向活跃客户端 {} 发送 Pong 失败: {}", client_id, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("活跃客户端 {} 连接错误: {}", client_id, e);
                    break;
                }
                _ => {
                    // 忽略其他消息类型
                }
            }
        }

        info!("活跃客户端 {} 连接已关闭", client_id);
    }

    /// 处理客户端消息
    async fn handle_client_message(
        text: String,
        connection: &ClientConnection,
        settings: &Arc<Settings>,
    ) {
        // 解析客户端消息
        let client_msg: ClientMessage = match serde_json::from_str(&text) {
            Ok(msg) => msg,
            Err(e) => {
                error!("解析客户端消息失败: {}", e);
                return;
            }
        };

        info!(
            "收到客户端 {} 的消息: {:?}",
            connection.id, client_msg.message_type
        );

        // 验证 Token（除了 verify_token 操作）
        if !matches!(client_msg.message_type, WsMessageType::VerifyToken) {
            if let Err(e) = Self::validate_token(&client_msg.token, settings).await {
                let response = ServerMessage {
                    id: client_msg.id,
                    code: 1,
                    msg: format!("认证失败: {}", e),
                    message_type: client_msg.message_type,
                };
                Self::send_response(connection, response).await;
                return;
            }
        }

        // 执行对应的操作
        let (code, msg) = match client_msg.message_type {
            WsMessageType::VerifyToken => {
                match Self::validate_token(&client_msg.token, settings).await {
                    Ok(_) => (0, "Token 验证成功".to_string()),
                    Err(e) => (1, format!("Token 验证失败: {}", e)),
                }
            }
            WsMessageType::InitOren => match init_oren_logic().await {
                Ok(_) => (0, "OpenResty Edge Node 初始化成功".to_string()),
                Err(e) => (1, format!("OpenResty Edge Node 初始化失败: {}", e)),
            },
            WsMessageType::AddOren => {
                // add_oren 是异步操作，立即返回成功，然后在后台逐年处理并通知客户端
                tokio::spawn(Self::handle_add_oren_async(
                    connection.sender.clone(),
                    client_msg.id.clone(),
                    settings.clone(),
                ));
                (
                    0,
                    "已启动添加 OpenResty Edge Node 流程，将逐年设定时间并通知进度".to_string(),
                )
            }
            WsMessageType::StartOren => match start_oren_logic().await {
                Ok(_) => (0, "OpenResty Edge Node 启动成功".to_string()),
                Err(e) => (1, format!("OpenResty Edge Node 启动失败: {}", e)),
            },
            WsMessageType::DoneOren => {
                match done_oren_logic().await {
                    Ok(_) => {
                        // 发送完成通知，告知客户端可以断开连接
                        let final_msg = SystemMessage {
                            msg_type: "workflow_complete".to_string(),
                            msg: "所有操作已完成，可以断开连接".to_string(),
                            data: None,
                        };

                        if let Ok(msg_json) = serde_json::to_string(&final_msg) {
                            let _ = connection.sender.send(Message::Text(msg_json));
                        }

                        (
                            0,
                            "OpenResty Edge Node 配置完成，整个流程已结束".to_string(),
                        )
                    }
                    Err(e) => (1, format!("OpenResty Edge Node 配置失败: {}", e)),
                }
            }
        };

        let response = ServerMessage {
            id: client_msg.id,
            code,
            msg,
            message_type: client_msg.message_type,
        };

        Self::send_response(connection, response).await;
    }

    /// 验证 Token
    async fn validate_token(
        token: &Option<String>,
        settings: &Arc<Settings>,
    ) -> Result<(), String> {
        let token = token.as_ref().ok_or("未提供认证 Token")?;

        if !token.starts_with("Bearer ") {
            return Err("Token 格式错误".to_string());
        }

        let token_value = &token[7..];

        if !settings
            .api
            .auth_tokens
            .iter()
            .any(|valid_token| valid_token == token_value)
        {
            return Err("Token 不匹配".to_string());
        }

        let current_date = chrono::Utc::now().naive_utc();
        if current_date > settings.api.token_expiry {
            return Err(format!("Token 已于 {} 过期", settings.api.token_expiry));
        }

        Ok(())
    }

    /// 发送响应消息
    async fn send_response(connection: &ClientConnection, response: ServerMessage) {
        if let Ok(response_json) = serde_json::to_string(&response) {
            if let Err(e) = connection.sender.send(Message::Text(response_json)) {
                error!("向客户端 {} 发送响应失败: {}", connection.id, e);
            }
        }
    }

    /// 异步处理 add_oren 操作 - 逐年设定时间并通知客户端
    async fn handle_add_oren_async(
        sender: mpsc::UnboundedSender<Message>,
        request_id: String,
        settings: Arc<Settings>,
    ) {
        info!("开始异步执行 add_oren 操作 - 逐年设定时间");

        // 发送开始通知
        let start_msg = SystemMessage {
            msg_type: "add_oren_start".to_string(),
            msg: "开始逐年设定时间，正在获取 NTP 时间...".to_string(),
            data: None,
        };

        if let Ok(msg_json) = serde_json::to_string(&start_msg) {
            let _ = sender.send(Message::Text(msg_json));
        }

        // 执行逐年设定逻辑
        match Self::execute_add_oren_with_progress(&sender, &settings).await {
            Ok(_) => {
                // 发送最终完成通知
                let complete_msg = ServerMessage {
                    id: request_id,
                    code: 0,
                    msg: "所有年份的时间设定已完成".to_string(),
                    message_type: WsMessageType::AddOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&complete_msg) {
                    let _ = sender.send(Message::Text(msg_json));
                }
            }
            Err(e) => {
                // 发送错误通知
                let error_msg = ServerMessage {
                    id: request_id,
                    code: 1,
                    msg: format!("添加操作失败: {}", e),
                    message_type: WsMessageType::AddOren,
                };

                if let Ok(msg_json) = serde_json::to_string(&error_msg) {
                    let _ = sender.send(Message::Text(msg_json));
                }
            }
        }
    }

    /// 执行 add_oren 逻辑并发送进度通知
    async fn execute_add_oren_with_progress(
        sender: &mpsc::UnboundedSender<Message>,
        settings: &Arc<Settings>,
    ) -> Result<(), crate::handlers::HandlerError> {
        use crate::handlers::HandlerError;
        use crate::utils::{execute_command_with_retry, get_current_datetime_from_ntp};
        use chrono::Datelike;

        let start_year = 2023;

        info!("现在获取 NTP 时间...");

        // 从 NTP 服务器获取当前完整日期时间
        let ntp_datetime = get_current_datetime_from_ntp(settings)
            .await
            .map_err(|e| HandlerError::NtpError(e.to_string()))?;

        // 获取 NTP 服务器提供的真实当前年份
        let current_year = ntp_datetime.year();

        info!(
            "NTP 当前时间: {}",
            ntp_datetime.format("%Y-%m-%d %H:%M:%S %z")
        );

        // 计算最后一个可设置的年份
        let end_year =
            if ntp_datetime.month() > 4 || (ntp_datetime.month() == 4 && ntp_datetime.day() > 20) {
                current_year
            } else {
                current_year - 1
            };

        let total_years = end_year - start_year;

        info!(
            "时间设定将从 {} 年至 {} 年, 共需处理 {} 年",
            start_year + 1,
            end_year,
            total_years
        );

        // 发送计算完成通知
        let calc_msg = SystemMessage {
            msg_type: "add_oren_calculated".to_string(),
            msg: format!(
                "计算完成，将从 {} 年设定至 {} 年，共 {} 年",
                start_year + 1,
                end_year,
                total_years
            ),
            data: Some(serde_json::json!({
                "start_year": start_year + 1,
                "end_year": end_year,
                "total_years": total_years
            })),
        };

        if let Ok(msg_json) = serde_json::to_string(&calc_msg) {
            let _ = sender.send(Message::Text(msg_json));
        }

        // 从 2024 年开始循环，到最后一个可设置的年份
        for year in (start_year + 1)..=end_year {
            let current_time = chrono::Local::now().format("%H:%M:%S").to_string();
            let set_time_arg = format!("{}-04-20 {}", year, current_time);

            info!("现在处理 {} 年...", year);
            info!("准备设定时间为: {}", set_time_arg);

            // 发送年份开始处理通知
            let year_start_msg = SystemMessage {
                msg_type: "add_oren_year_start".to_string(),
                msg: format!("开始处理 {} 年，设定时间为: {}", year, set_time_arg),
                data: Some(serde_json::json!({
                    "year": year,
                    "time": set_time_arg
                })),
            };

            if let Ok(msg_json) = serde_json::to_string(&year_start_msg) {
                let _ = sender.send(Message::Text(msg_json));
            }

            // 设置系统时间
            execute_command_with_retry("timedatectl", &["set-time", &set_time_arg], "时间设置失败")
                .map_err(HandlerError::from)?;

            info!("时间已设定为: {}", set_time_arg);
            info!("现在重启 OpenResty Edge Admin...");

            // 重启相关服务
            execute_command_with_retry(
                "systemctl",
                &[
                    "restart",
                    "oredge-admin",
                    "oredge-log-server",
                    "openresty-postgresql12",
                ],
                "OpenResty Edge Admin 重启失败",
            )
            .map_err(HandlerError::from)?;

            // 服务重启完成，时间设定立即生效

            info!("设定 {} 年已完成", year);

            // 发送年份完成通知 - 只有在时间设定和服务重启都完成后才通知客户端
            let year_complete_msg = SystemMessage {
                msg_type: "add_oren_year_complete".to_string(),
                msg: format!(
                    "{} 年时间设定和服务重启已完成，客户端可以执行相应操作",
                    year
                ),
                data: Some(serde_json::json!({
                    "year": year,
                    "completed_time": set_time_arg,
                    "services_restarted": true
                })),
            };

            if let Ok(msg_json) = serde_json::to_string(&year_complete_msg) {
                let _ = sender.send(Message::Text(msg_json));
            }
        }

        info!("所有时间设定已完成");
        Ok(())
    }
}

/// 全局连接管理器状态
pub struct AppState {
    pub connection_manager: ConnectionManager,
    pub settings: Arc<Settings>,
}

impl AppState {
    pub fn new(settings: Arc<Settings>) -> Self {
        let (connection_manager, _queue_receiver) = ConnectionManager::new();
        Self {
            connection_manager,
            settings,
        }
    }
}

/// WebSocket 升级处理函数
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(settings): State<Arc<Settings>>,
) -> Response {
    // 暂时直接处理连接，后续可以集成全局连接管理器
    ws.on_upgrade(move |socket| async move {
        let client_id = Uuid::new_v4();
        ConnectionManager::handle_active_client(socket, client_id, settings).await;
    })
}
