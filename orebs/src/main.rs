mod basic;
mod handlers;
mod oredge_cli;
mod utils;
mod websocket;

use basic::boot::Bootstrap;

use log::{error, info};
use axum::{routing::get, Router};
use std::net::SocketAddr;
use thiserror::Error;

/// 主程序错误类型
#[derive(Debug, Error)]
pub enum AppError {
    /// 服务器地址解析错误
    #[error("无效的服务器地址 {addr}: {source}")]
    InvalidAddress {
        addr: String,
        source: std::net::AddrParseError,
    },
    /// 启动错误
    #[error("应用启动失败: {0}")]
    Bootstrap(#[from] basic::boot::BootstrapError),
    /// 服务器错误
    #[error("服务器错误: {0}")]
    Server(#[from] std::io::Error),
}

#[tokio::main]
async fn main() -> Result<(), AppError> {
    // 初始化应用
    let bootstrap = Bootstrap::new().map_err(AppError::Bootstrap)?;

    // 构建服务器地址
    let server_addr = format!(
        "{}:{}",
        bootstrap.settings.server.host,
        bootstrap.settings.server.port.unwrap_or(8080)
    );

    let addr = server_addr
        .parse::<SocketAddr>()
        .map_err(|e| AppError::InvalidAddress {
            addr: server_addr.clone(),
            source: e,
        })?;

    // 构建路由
    let app = Router::new()
        .route("/ws", get(websocket::websocket_handler))
        .with_state(bootstrap.settings);

    info!("服务正在监听地址: {}", addr);
    info!("服务连接地址: ws://{}/ws", addr);

    // 创建 TCP 监听器并启动服务
    let listener = tokio::net::TcpListener::bind(&addr)
        .await
        .map_err(AppError::Server)?;

    info!("服务已准备就绪, 等待客户端连接...");

    axum::serve(listener, app).await.map_err(AppError::Server)?;

    Ok(())
}
