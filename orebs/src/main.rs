mod auth;
mod basic;
mod handlers;
mod oredge_cli;
mod utils;
mod websocket;

use basic::boot::Bootstrap;
use log::{error, info};

use axum::{
    body::Body,
    error_handling::HandleErrorLayer,
    http::{Request, Response, StatusCode},
    middleware::Next,
    response::IntoResponse,
    routing::get,
    Router,
};
use std::{net::SocketAddr, time::Duration};
use thiserror::Error;
use tower::{BoxError, ServiceBuilder};
use tower_http::trace::TraceLayer;

/// 主程序错误类型
#[derive(Debug, Error)]
pub enum AppError {
    /// 服务器地址解析错误
    #[error("无效的服务器地址 {addr}: {source}")]
    InvalidAddress {
        addr: String,
        source: std::net::AddrParseError,
    },
    /// 启动错误
    #[error("应用启动失败: {0}")]
    Bootstrap(#[from] basic::boot::BootstrapError),
    /// HTTP 服务器错误
    #[error("HTTP 服务器错误: {0}")]
    Server(#[from] std::io::Error),
}

/// 请求日志中间件
async fn log_request(req: Request<Body>, next: Next) -> Response<Body> {
    let method = req.method();
    let uri = req.uri();
    let headers = req.headers();
    info!("收到请求: {} {} {:?}", method, uri, headers);
    next.run(req).await
}

/// 错误处理函数
async fn handle_error(error: BoxError) -> Response<Body> {
    if error.is::<tower::timeout::error::Elapsed>() {
        return StatusCode::REQUEST_TIMEOUT.into_response();
    }
    StatusCode::INTERNAL_SERVER_ERROR.into_response()
}

#[tokio::main]
async fn main() -> Result<(), AppError> {
    // 初始化应用
    let bootstrap = Bootstrap::new().map_err(AppError::Bootstrap)?;

    // 构建服务器地址
    let server_addr = format!(
        "{}:{}",
        bootstrap.settings.server.host,
        bootstrap.settings.server.port.unwrap_or(8080)
    );

    let addr = server_addr
        .parse::<SocketAddr>()
        .map_err(|e| AppError::InvalidAddress {
            addr: server_addr.clone(),
            source: e,
        })?;

    // 构建路由
    let app = Router::new()
        .route("/verify_token", get(handlers::verify_token))
        .route("/init_oren", get(handlers::init_oren))
        .route("/add_oren", get(handlers::add_oren))
        .route("/start_oren", get(handlers::start_oren))
        .route("/done_oren", get(handlers::done_oren))
        .fallback(handlers::fallback)
        .layer(
            ServiceBuilder::new()
                .layer(HandleErrorLayer::new(handle_error))
                .timeout(Duration::from_secs(30))
                .layer(TraceLayer::new_for_http())
                .layer(axum::middleware::from_fn(log_request))
                .layer(axum::middleware::from_fn(auth::check_ipv4))
                .layer(axum::middleware::from_fn_with_state(
                    bootstrap.settings.clone(),
                    auth::auth,
                ))
                .into_inner(),
        )
        .with_state(bootstrap.settings);

    info!("正在监听地址: {}", addr);

    // 创建 TCP 监听器并启动服务
    let listener = tokio::net::TcpListener::bind(&addr)
        .await
        .map_err(AppError::Server)?;

    info!("服务已准备就绪, 等待连接...");

    axum::serve(listener, app).await.map_err(AppError::Server)?;

    Ok(())
}
