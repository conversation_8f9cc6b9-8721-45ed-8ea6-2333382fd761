use crate::{
    basic::config::types::Settings,
    oredge_cli::execute_oredge_cli,
    utils::{execute_command_with_retry, get_current_datetime_from_ntp, UtilsError},
};
use log::{error, info};

use axum::extract::State;
use axum::{response::IntoResponse, Json};
use chrono::Datelike;
use std::sync::Arc;
use std::time::Instant;
use thiserror::Error;
use tokio::time::{sleep, Duration};

/// 处理器错误类型
#[derive(Debug, Error)]
pub enum HandlerError {
    /// NTP 同步错误
    #[error("NTP 同步失败: {0}")]
    NtpError(String),

    /// CLI 执行错误
    #[error("CLI 执行失败: {0}")]
    CliError(String),

    /// 工具错误
    #[error("工具执行错误: {0}")]
    Utils(#[from] UtilsError),
}

/// API 响应结构
#[derive(serde::Serialize)]
struct ApiResponse {
    /// 响应代码: 0 表示成功, 非 0 表示失败
    code: i32,
    /// 响应消息
    msg: String,
}

/// 处理错误并返回统一的响应格式
async fn handle_result<F, Fut>(f: F, success_msg: &str, error_msg: &str) -> impl IntoResponse
where
    F: FnOnce() -> Fut,
    Fut: std::future::Future<Output = Result<(), HandlerError>>,
{
    match f().await {
        Ok(_) => Json(ApiResponse {
            code: 0,
            msg: success_msg.to_string(),
        }),
        Err(e) => {
            log::error!("{}: {}", error_msg, e);
            Json(ApiResponse {
                code: 1,
                msg: format!("{}: {}", error_msg, e),
            })
        }
    }
}

/// 验证 Token 业务逻辑
pub async fn verify_token_logic() -> Result<(), HandlerError> {
    info!("开始验证 Token...");
    Ok(())
}

/// 验证 Token (HTTP 接口，保留用于兼容)
pub async fn verify_token() -> impl IntoResponse {
    handle_result(
        verify_token_logic,
        "验证成功",
        "验证失败",
    )
    .await
}

/// 初始化 OpenResty Edge Node
pub async fn init_oren() -> impl IntoResponse {
    info!("开始初始化 OpenResty Edge Node...");

    handle_result(
        || async {
            info!("现在关闭 NTP 同步...");

            // 关闭 NTP 同步
            execute_command_with_retry("timedatectl", &["set-ntp", "off"], "NTP 同步关闭失败")
                .map_err(HandlerError::from)?;

            info!("现在初始化时间至 2023-05-01...");

            // 设置初始时间
            let current_time = chrono::Local::now().format("%H:%M:%S").to_string();
            let set_time_arg = format!("2023-05-01 {}", current_time);

            execute_command_with_retry(
                "timedatectl",
                &["set-time", &set_time_arg],
                "时间初始化失败",
            )
            .map_err(HandlerError::from)?;

            info!("时间已初始化为: {}", set_time_arg);
            info!("现在重启 OpenResty Edge Admin...");

            // 重启相关服务
            execute_command_with_retry(
                "systemctl",
                &[
                    "restart",
                    "oredge-admin",
                    "oredge-log-server",
                    "openresty-postgresql12",
                ],
                "OpenResty Edge Admin 重启失败",
            )
            .map_err(HandlerError::from)?;

            Ok(())
        },
        "OpenResty Edge Node 初始化成功",
        "OpenResty Edge Node 初始化失败",
    )
    .await
}

/// 添加 OpenResty Edge Node
pub async fn add_oren(State(settings): State<Arc<Settings>>) -> impl IntoResponse {
    info!("开始添加 OpenResty Edge Node...");

    // 为了在 tokio::spawn 的异步任务中使用 settings, 我们需要克隆它.
    // Arc<Settings> 的克隆是非常轻量的, 仅增加引用计数.
    let settings_for_task = Arc::clone(&settings);

    tokio::spawn(async move {
        // 将原有的核心处理逻辑封装在一个异步闭包中, 方便后续的错误处理和日志记录.
        let task_logic = || async {
            let start_year = 2023;

            info!("现在获取 NTP 时间...");

            // 从 NTP 服务器获取当前完整日期时间, 而非系统时间
            // 注意: 这里使用的是 settings_for_task
            let ntp_datetime = get_current_datetime_from_ntp(&settings_for_task)
                .await
                .map_err(|e| HandlerError::NtpError(e.to_string()))?;

            // 获取 NTP 服务器提供的真实当前年份
            let current_year = ntp_datetime.year();

            info!(
                "NTP 当前时间: {}",
                ntp_datetime.format("%Y-%m-%d %H:%M:%S %z")
            );

            info!("开始计算时间范围...");

            // 计算最后一个可设置的年份, 使用 NTP 时间而非系统时间
            let end_year = if ntp_datetime.month() > 4
                || (ntp_datetime.month() == 4 && ntp_datetime.day() > 20)
            {
                // 当前日期在 4 月 20 日之后的情况:
                // 1. 月份大于 4 (5-12月)
                // 2. 或者是 4 月但日期大于 20 日 (4月21-30日)
                // 这种情况下可以设置到当年的 4 月 20 日
                current_year
            } else {
                // 当前日期在 4 月 20 日及之前的情况:
                // 1. 月份小于 4 (1-3月)
                // 2. 或者是 4 月但日期小于等于 20 日 (4月1-20日)
                // 这种情况下只能设置到去年的 4 月 20 日
                current_year - 1
            };

            let total_years = end_year - start_year;

            info!(
                "NTP 日期: {}-{:02}-{:02}, 当前年份: {}, 结束年份: {}",
                ntp_datetime.year(),
                ntp_datetime.month(),
                ntp_datetime.day(),
                current_year,
                end_year
            );

            info!(
                "时间设定将从 {} 年至 {} 年, 共需处理 {} 年",
                start_year + 1,
                end_year,
                total_years
            );

            info!("现在执行年份循环处理...");

            // 从 2024 年开始循环, 到最后一个可设置的年份
            for year in (start_year + 1)..=end_year {
                let current_time = chrono::Local::now().format("%H:%M:%S").to_string();
                let set_time_arg = format!("{}-04-20 {}", year, current_time);

                info!("现在处理 {} 年...", year);
                info!("准备设定时间为: {}", set_time_arg);

                // 设置系统时间
                execute_command_with_retry(
                    "timedatectl",
                    &["set-time", &set_time_arg],
                    "时间设置失败",
                )
                .map_err(HandlerError::from)?;

                info!("时间已设定为: {}", set_time_arg);
                info!("现在重启 OpenResty Edge Admin...");

                // 重启相关服务
                execute_command_with_retry(
                    "systemctl",
                    &[
                        "restart",
                        "oredge-admin",
                        "oredge-log-server",
                        "openresty-postgresql12",
                    ],
                    "OpenResty Edge Admin 重启失败",
                )
                .map_err(HandlerError::from)?;

                // 等待服务完全启动
                info!("等待服务启动完成...");
                tokio::time::sleep(Duration::from_secs(6)).await;

                info!("设定 {} 年已完成", year);
            }

            info!("所有时间设定已完成");
            Ok::<(), HandlerError>(())
        };

        // 执行实际的逐年处理逻辑并处理其结果 (仅用于日志记录, 不再作为 API 响应体返回给客户端)
        match task_logic().await {
            Ok(_) => {
                info!("OpenResty Edge Node 添加成功");
            }
            Err(e) => {
                error!("OpenResty Edge Node 添加失败: {}", e);
            }
        }
    });

    // API 接口立即返回, 告知客户端后台任务已启动. 这里不再使用 handle_result 函数返回任何响应体.
    Json(ApiResponse {
        code: 0,
        msg: "已启动添加 OpenResty Edge Node 流程".to_string(),
    })
}

/// 启动 OpenResty Edge Node
pub async fn start_oren() -> impl IntoResponse {
    info!("开始启动 OpenResty Edge Node...");

    handle_result(
        || async {
            info!("现在开启 NTP 同步...");

            execute_command_with_retry("timedatectl", &["set-ntp", "on"], "NTP 同步开启失败")
                .map_err(HandlerError::from)?;

            info!("等待 NTP 同步完成...");
            sleep(Duration::from_secs(10)).await;

            info!("现在重启 OpenResty Edge Admin...");
            execute_command_with_retry(
                "systemctl",
                &[
                    "restart",
                    "oredge-admin",
                    "oredge-log-server",
                    "openresty-postgresql12",
                ],
                "OpenResty Edge Admin 重启失败",
            )
            .map_err(HandlerError::from)?;

            Ok(())
        },
        "OpenResty Edge Node 启动成功",
        "OpenResty Edge Node 启动失败",
    )
    .await
}

/// 完成 OpenResty Edge Node
pub async fn done_oren() -> impl IntoResponse {
    info!("开始完成 OpenResty Edge Node 配置...");

    let start_time = Instant::now();

    handle_result(
        move || async move {
            info!("现在初始化配置...");

            execute_oredge_cli(
                &[
                    "global-config",
                    "modify",
                    "1",
                    "--set-real-ip-from",
                    "*******",
                    "--json",
                ],
                start_time,
            )
            .map_err(|e| HandlerError::CliError(e.to_string()))?;

            info!("等待配置发布生效...");
            sleep(Duration::from_secs(5)).await;

            info!("开始完成初始化配置...");
            execute_oredge_cli(
                &[
                    "global-config",
                    "modify",
                    "1",
                    "--set-real-ip-from",
                    "0.0.0.0/0",
                    "--json",
                ],
                start_time,
            )
            .map_err(|e| HandlerError::CliError(e.to_string()))?;

            Ok(())
        },
        "OpenResty Edge Node 配置完成",
        "OpenResty Edge Node 配置失败",
    )
    .await
}

/// 处理未匹配的路由
pub async fn fallback() -> impl IntoResponse {
    info!("收到未匹配的路由请求...");

    Json(ApiResponse {
        code: 1,
        msg: "服务运行中...".to_string(),
    })
}
