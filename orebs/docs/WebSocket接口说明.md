# WebSocket 协议文档

## 连接地址
```
ws://host:port/ws
```

## 消息格式

### 客户端请求消息
```json
{
  "id": "unique_request_id",
  "type": "操作类型",
  "token": "Bearer your_token_here"
}
```

### 服务端响应消息
```json
{
  "id": "对应的请求ID",
  "code": 0,
  "msg": "响应消息",
  "type": "操作类型"
}
```

### 系统通知消息
```json
{
  "type": "通知类型",
  "msg": "通知内容",
  "data": {
    // 可选的额外数据
  }
}
```

## 操作类型

### 1. verify_token - Token验证
**客户端请求:**
```json
{
  "id": "req_001",
  "type": "verify_token",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_001",
  "code": 0,
  "msg": "Token 验证成功",
  "type": "verify_token"
}
```

### 2. init_oren - 初始化操作
**客户端请求:**
```json
{
  "id": "req_002",
  "type": "init_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_002",
  "code": 0,
  "msg": "OpenResty Edge Node 初始化成功",
  "type": "init_oren"
}
```

### 3. add_oren - 逐年设定时间
**客户端请求:**
```json
{
  "id": "req_003",
  "type": "add_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端立即响应:**
```json
{
  "id": "req_003",
  "code": 0,
  "msg": "已启动添加 OpenResty Edge Node 流程，将逐年设定时间并通知进度",
  "type": "add_oren"
}
```

**服务端进度通知:**
```json
{
  "type": "add_oren_start",
  "msg": "开始逐年设定时间，正在获取 NTP 时间...",
  "data": null
}
```

```json
{
  "type": "add_oren_calculated",
  "msg": "计算完成，将从 2024 年设定至 2025 年，共 2 年",
  "data": {
    "start_year": 2024,
    "end_year": 2025,
    "total_years": 2
  }
}
```

```json
{
  "type": "add_oren_year_start",
  "msg": "开始处理 2024 年，设定时间为: 2024-04-20 14:30:00",
  "data": {
    "year": 2024,
    "time": "2024-04-20 14:30:00"
  }
}
```

```json
{
  "type": "add_oren_year_complete",
  "msg": "2024 年时间设定和服务重启已完成，客户端可以执行相应操作",
  "data": {
    "year": 2024,
    "completed_time": "2024-04-20 14:30:00",
    "services_restarted": true
  }
}
```

**最终完成通知:**
```json
{
  "id": "req_003",
  "code": 0,
  "msg": "所有年份的时间设定已完成",
  "type": "add_oren"
}
```

### 4. start_oren - 启动操作
**客户端请求:**
```json
{
  "id": "req_004",
  "type": "start_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_004",
  "code": 0,
  "msg": "OpenResty Edge Node 启动成功",
  "type": "start_oren"
}
```

### 5. done_oren - 完成操作
**客户端请求:**
```json
{
  "id": "req_005",
  "type": "done_oren",
  "token": "Bearer MTAuMC4wLjEwMA=="
}
```

**服务端响应:**
```json
{
  "id": "req_005",
  "code": 0,
  "msg": "OpenResty Edge Node 配置完成，整个流程已结束",
  "type": "done_oren"
}
```

**工作流完成通知:**
```json
{
  "type": "workflow_complete",
  "msg": "所有操作已完成，可以断开连接",
  "data": null
}
```

## 系统通知类型

### 连接状态通知
- `connected` - 连接成功
- `waiting` - 排队等待
- `workflow_complete` - 整个工作流程完成

### add_oren 进度通知
- `add_oren_start` - 开始逐年设定
- `add_oren_calculated` - 计算完成
- `add_oren_year_start` - 开始处理某年
- `add_oren_year_complete` - 某年处理完成

## 重要说明

### 操作完成的定义
- **init_oren, add_oren, start_oren**: 这三个操作涉及时间设定，只有当**时间设定完成 + 相关服务重启完成**后才算操作完成
- **add_oren**: 每年的处理包括时间设定和服务重启，时间设定立即生效，服务重启完成后立即发送 `add_oren_year_complete` 通知
- **start_oren**: 包含10秒的NTP同步等待时间，确保NTP同步完成后再重启服务
- **done_oren**: 包含5秒的配置发布等待时间，确保配置下发完成

### 时间延迟说明
- **add_oren**: 时间设定立即生效，服务重启完成后立即通知（无额外延迟）
- **start_oren**: 等待10秒确保NTP同步完成
- **done_oren**: 等待5秒确保配置发布生效

## 业务流程

1. **连接建立** → 收到 `connected` 通知
2. **Token验证** → `verify_token`
3. **初始化** → `init_oren` (时间设定 + 服务重启)
4. **逐年设定** → `add_oren` (每年: 时间设定 + 服务重启 + 6秒等待)
5. **启动服务** → `start_oren` (NTP同步 + 10秒等待 + 服务重启)
6. **完成配置** → `done_oren` (配置发布 + 5秒等待) → 收到 `workflow_complete` 通知
7. **断开连接**

## 错误处理

当操作失败时，响应的 `code` 字段为非0值：
```json
{
  "id": "req_001",
  "code": 1,
  "msg": "Token 验证失败: Token 格式错误",
  "type": "verify_token"
}
```

## 连接管理

- 同时只允许一个客户端连接
- 其他客户端会收到排队通知
- 当前客户端断开后，下一个排队的客户端会被激活
